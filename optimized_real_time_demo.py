"""
优化后实时预测演示
集成所有优化模块的实时预测系统
"""

import asyncio
import time
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from sklearn.preprocessing import MinMaxScaler
import matplotlib.pyplot as plt
import warnings
from collections import deque
import os

# 导入原有模块
from real_time_stream_processor import RealTimeStreamProcessor
from real_time_data_bridge import DSADataSource, RealTimeVisualizationBridge
from real_time_train import train_displacement_model    

# 导入优化模块
from data_quality_optimizer import DataQualityOptimizer, optimize_training_data
from model_architecture_optimizer import ModelArchitectureOptimizer, EnhancedLSTM
from training_strategy_optimizer import TrainingStrategyOptimizer, create_data_loaders
from akf_parameter_optimizer import OptimizedAdaptiveKalmanFilter, AKFParameterOptimizer
from ensemble_learning import EnsemblePredictor, DiversityBasedEnsemble

warnings.filterwarnings("ignore")

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    print("中文字体设置成功")
except:
    print("使用默认字体")


class OptimizedRealTimePredictor:
    """优化后的实时预测器"""
    
    def __init__(self, window_size=25):
        self.window_size = window_size
        self.model = None
        self.scaler = None
        self.akf = None
        self.ensemble_predictor = None
        self.data_buffer = deque(maxlen=window_size)
        self.prediction_history = []
        self.actual_history = []
        self.performance_metrics = {
            'mae': [],
            'rmse': [],
            'mape': []
        }
        
    def train_optimized_model(self, data_file='v1.txt'):
        """训练优化后的模型"""
        print("🚀 开始训练优化后的模型...")
        
        # 1. 数据质量优化
        print("步骤1: 数据质量优化")
        success = optimize_training_data(data_file, 'v1_optimized.txt')
        if success:
            data_file = 'v1_optimized.txt'
            print("✅ 数据质量优化完成")
        
        # 2. 加载和预处理数据
        print("步骤2: 加载数据")
        data = pd.read_csv(data_file, sep='\t', encoding='utf-8')
        displacement_data = data.iloc[:, 1].values
        
        train_points = min(1000, len(displacement_data) - self.window_size)
        train_data = displacement_data[:train_points + self.window_size]
        
        # 创建序列
        X, y = self._create_sequences(train_data)
        
        # 数据标准化
        self.scaler = MinMaxScaler(feature_range=(-1, 1))
        flat_data = train_data.reshape(-1, 1)
        self.scaler.fit(flat_data)
        
        # 标准化序列数据
        X_normalized = []
        for seq in X:
            norm_seq = self.scaler.transform(seq.reshape(-1, 1)).flatten()
            X_normalized.append(norm_seq)
        
        y_normalized = self.scaler.transform(y.reshape(-1, 1)).flatten()
        
        # 转换为张量
        X_tensor = torch.FloatTensor(X_normalized).unsqueeze(-1)
        y_tensor = torch.FloatTensor(y_normalized)
        
        # 3. 模型架构优化
        print("步骤3: 模型架构优化")
        arch_optimizer = ModelArchitectureOptimizer()
        
        # 比较架构并选择最佳
        arch_results = arch_optimizer.compare_model_architectures(
            X_tensor[:100], y_tensor[:100], 
            architectures=['basic_lstm', 'enhanced_lstm']
        )
        
        valid_archs = {k: v for k, v in arch_results.items() if 'loss' in v}
        if valid_archs:
            best_arch = min(valid_archs.keys(), key=lambda k: valid_archs[k]['loss'])
            print(f"选择最佳架构: {best_arch}")
            self.model = arch_optimizer.create_model(best_arch, input_size=1, output_size=1)
        else:
            self.model = EnhancedLSTM(input_size=1, hidden_size=128, num_layers=2)
        
        # 4. 训练策略优化
        print("步骤4: 高级训练策略")
        strategy_optimizer = TrainingStrategyOptimizer()
        trainer = strategy_optimizer.create_trainer(self.model, strategy_name='advanced')
        
        # 创建数据加载器
        train_loader, val_loader = create_data_loaders(
            X_tensor, y_tensor, batch_size=32, val_split=0.2
        )
        
        # 训练模型
        history = trainer.train(train_loader, val_loader, epochs=100, verbose=False)
        print(f"训练完成，最佳验证损失: {history['best_val_loss']:.6f}")
        
        # 5. AKF参数优化
        print("步骤5: AKF参数优化")
        
        # 生成一些预测用于参数优化
        test_predictions = []
        test_actuals = []
        
        self.model.eval()
        with torch.no_grad():
            for i in range(min(200, len(X_tensor))):
                pred = self.model(X_tensor[i:i+1]).item()
                pred_denorm = self.scaler.inverse_transform([[pred]])[0, 0]
                actual_denorm = self.scaler.inverse_transform([[y_tensor[i].item()]])[0, 0]
                
                test_predictions.append(pred_denorm)
                test_actuals.append(actual_denorm)
        
        # 优化AKF参数
        akf_optimizer = AKFParameterOptimizer()
        optimization_result = akf_optimizer.optimize_parameters(
            test_predictions, test_actuals, test_actuals,
            method='differential_evolution', max_evaluations=30
        )
        
        # 创建优化后的AKF
        best_params = optimization_result['best_params']
        self.akf = OptimizedAdaptiveKalmanFilter(
            process_variance=best_params['process_variance'],
            measurement_variance=best_params['measurement_variance'],
            adaptation_rate=best_params['adaptation_rate'],
            adaptive_strategy='hybrid'
        )
        
        print("✅ 优化后模型训练完成")
        return True
    
    def _create_sequences(self, data):
        """创建训练序列"""
        sequences = []
        targets = []
        
        for i in range(len(data) - self.window_size):
            seq = data[i:i+self.window_size]
            target = data[i+self.window_size]
            sequences.append(seq)
            targets.append(target)
        
        return np.array(sequences), np.array(targets)
    
    def predict_single(self, new_value, actual_value=None):
        """单点预测"""
        # 添加到缓冲区
        self.data_buffer.append(new_value)
        
        if len(self.data_buffer) < self.window_size:
            return new_value  # 缓冲区未满，返回原值
        
        # LSTM预测
        input_seq = np.array(list(self.data_buffer))
        normalized_seq = self.scaler.transform(input_seq.reshape(-1, 1)).flatten()
        input_tensor = torch.FloatTensor(normalized_seq).unsqueeze(0).unsqueeze(-1)
        
        self.model.eval()
        with torch.no_grad():
            normalized_pred = self.model(input_tensor).item()
        
        lstm_pred = self.scaler.inverse_transform([[normalized_pred]])[0, 0]
        
        # AKF滤波
        if self.akf is not None:
            akf_pred = self.akf.predict_and_update(new_value, lstm_pred, actual_value)
            final_pred = akf_pred
        else:
            final_pred = lstm_pred
        
        # 记录历史
        self.prediction_history.append(final_pred)
        if actual_value is not None:
            self.actual_history.append(actual_value)
            self._update_metrics(final_pred, actual_value)
        
        return final_pred
    
    def _update_metrics(self, prediction, actual):
        """更新性能指标"""
        if len(self.actual_history) > 0:
            recent_preds = self.prediction_history[-min(100, len(self.prediction_history)):]
            recent_actuals = self.actual_history[-min(100, len(self.actual_history)):]
            
            if len(recent_preds) == len(recent_actuals) and len(recent_preds) > 0:
                mae = np.mean(np.abs(np.array(recent_preds) - np.array(recent_actuals)))
                rmse = np.sqrt(np.mean((np.array(recent_preds) - np.array(recent_actuals)) ** 2))
                mape = np.mean(np.abs((np.array(recent_preds) - np.array(recent_actuals)) / np.array(recent_actuals))) * 100
                
                self.performance_metrics['mae'].append(mae)
                self.performance_metrics['rmse'].append(rmse)
                self.performance_metrics['mape'].append(mape)
    
    def get_current_metrics(self):
        """获取当前性能指标"""
        if not self.performance_metrics['mae']:
            return None
        
        return {
            'mae': self.performance_metrics['mae'][-1] if self.performance_metrics['mae'] else 0,
            'rmse': self.performance_metrics['rmse'][-1] if self.performance_metrics['rmse'] else 0,
            'mape': self.performance_metrics['mape'][-1] if self.performance_metrics['mape'] else 0
        }


async def collect_optimized_real_time_data(predictor, displacement_source, duration_seconds=10):
    """收集优化后的实时预测数据"""
    print(f"📊 开始收集{duration_seconds}秒优化预测数据...")
    
    data_records = []
    start_time = time.time()
    sample_count = 0
    
    while time.time() - start_time < duration_seconds:
        try:
            # 获取实时位移数据
            displacement_data = await displacement_source.get_data()
            
            if displacement_data and len(displacement_data) > 0:
                current_time = time.time() - start_time
                displacement_value = displacement_data[0]
                
                # 优化预测
                predicted_value = predictor.predict_single(displacement_value, displacement_value)
                
                # 记录数据
                data_records.append({
                    'time': current_time,
                    'displacement': displacement_value,
                    'prediction': predicted_value,
                    'error': abs(predicted_value - displacement_value)
                })
                
                sample_count += 1
                
                # 每1000个样本显示一次进度
                if sample_count % 1000 == 0:
                    metrics = predictor.get_current_metrics()
                    if metrics:
                        print(f"  📈 样本{sample_count}: MAE={metrics['mae']:.6f}μm, RMSE={metrics['rmse']:.6f}μm")
                
            await asyncio.sleep(0.0001)  # 10kHz采样率
            
        except Exception as e:
            print(f"数据收集错误: {e}")
            continue
    
    print(f"✅ 收集完成，共{len(data_records)}个数据点")
    return data_records


async def main():
    """主函数"""
    print("🚀 优化后实时预测演示")
    print("=" * 60)
    
    # 1. 初始化优化预测器
    print("步骤1: 初始化优化预测器")
    predictor = OptimizedRealTimePredictor(window_size=25)
    
    # 2. 训练优化模型
    print("\n步骤2: 训练优化模型")
    if not predictor.train_optimized_model():
        print("❌ 模型训练失败")
        return
    
    # 3. 初始化数据源
    print("\n步骤3: 初始化数据源")
    bridge = RealTimeVisualizationBridge(dll_path="DSA_DLL.dll")
    displacement_source = DSADataSource(
        dll_path="DSA_DLL.dll",
        data_type='displacement'
    )

    # 启动数据收集
    bridge.add_source("displacement", displacement_source)
    bridge.start_all()
    
    print("⏳ 等待数据源稳定...")
    await asyncio.sleep(2)
    
    # 4. 实时预测
    print("\n步骤4: 开始实时预测")
    prediction_data = await collect_optimized_real_time_data(
        predictor, displacement_source, duration_seconds=10
    )
    
    # 5. 保存结果
    print("\n步骤5: 保存预测结果")
    df = pd.DataFrame(prediction_data)
    filename = f"optimized_prediction_{int(time.time())}.csv"
    df.to_csv(filename, index=False)
    print(f"✅ 结果已保存: {filename}")
    
    # 6. 性能分析
    print("\n步骤6: 性能分析")
    final_metrics = predictor.get_current_metrics()
    if final_metrics:
        print(f"📊 最终性能指标:")
        print(f"  MAE: {final_metrics['mae']:.6f} μm")
        print(f"  RMSE: {final_metrics['rmse']:.6f} μm")
        print(f"  MAPE: {final_metrics['mape']:.2f}%")
    
    # 7. 生成可视化
    print("\n步骤7: 生成可视化图表")
    create_real_time_visualization(df)
    
    # 8. 停止数据收集
    bridge.stop_all()
    
    print("\n" + "="*60)
    print("✅ 优化后实时预测演示完成！")
    print("="*60)


def create_real_time_visualization(df):
    """创建实时预测可视化"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('优化后实时预测性能', fontsize=16, fontweight='bold')
    
    # 预测对比
    axes[0, 0].plot(df['time'], df['displacement'], 'b-', label='实际值', alpha=0.7)
    axes[0, 0].plot(df['time'], df['prediction'], 'r--', label='预测值', alpha=0.7)
    axes[0, 0].set_xlabel('时间 (s)')
    axes[0, 0].set_ylabel('位移 (μm)')
    axes[0, 0].set_title('实时预测对比')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 预测误差
    axes[0, 1].plot(df['time'], df['error'], 'g-', alpha=0.7)
    axes[0, 1].set_xlabel('时间 (s)')
    axes[0, 1].set_ylabel('绝对误差 (μm)')
    axes[0, 1].set_title('预测误差变化')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 误差分布
    axes[1, 0].hist(df['error'], bins=50, alpha=0.7, color='orange')
    axes[1, 0].set_xlabel('绝对误差 (μm)')
    axes[1, 0].set_ylabel('频次')
    axes[1, 0].set_title('误差分布')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 散点图
    axes[1, 1].scatter(df['displacement'], df['prediction'], alpha=0.5, s=1)
    axes[1, 1].plot([df['displacement'].min(), df['displacement'].max()], 
                    [df['displacement'].min(), df['displacement'].max()], 'r--', alpha=0.8)
    axes[1, 1].set_xlabel('实际值 (μm)')
    axes[1, 1].set_ylabel('预测值 (μm)')
    axes[1, 1].set_title('预测精度散点图')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('optimized_real_time_prediction.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 可视化图表已保存: optimized_real_time_prediction.png")


if __name__ == "__main__":
    asyncio.run(main())
