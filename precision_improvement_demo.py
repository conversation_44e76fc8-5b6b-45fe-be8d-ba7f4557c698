"""
精度提升综合演示脚本
整合所有优化模块，展示预测精度提升效果
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from sklearn.preprocessing import MinMaxScaler
import matplotlib.pyplot as plt
import warnings
import time

# 导入优化模块
from data_quality_optimizer import DataQualityOptimizer, optimize_training_data
from model_architecture_optimizer import ModelArchitectureOptimizer, EnhancedLSTM
from training_strategy_optimizer import TrainingStrategyOptimizer, create_data_loaders
from akf_parameter_optimizer import OptimizedAdaptiveKalmanFilter, AKFParameterOptimizer

warnings.filterwarnings("ignore")

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    print("中文字体设置成功")
except:
    print("使用默认字体")


def create_sequences(data, window_size):
    """创建训练序列"""
    sequences = []
    targets = []
    
    for i in range(len(data) - window_size):
        seq = data[i:i+window_size]
        target = data[i+window_size]
        sequences.append(seq)
        targets.append(target)
    
    return np.array(sequences), np.array(targets)


def train_optimized_model(data_file='v1.txt', use_optimizations=True):
    """训练优化后的模型"""
    print("=" * 60)
    print("开始训练优化后的模型")
    print("=" * 60)
    
    # 1. 数据质量优化
    if use_optimizations:
        print("\n步骤1: 数据质量优化")
        success = optimize_training_data(data_file, 'v1_optimized.txt')
        if success:
            data_file = 'v1_optimized.txt'
            print("✅ 数据质量优化完成")
        else:
            print("⚠️ 数据质量优化失败，使用原始数据")
    
    # 2. 加载数据
    print(f"\n步骤2: 加载数据文件 {data_file}")
    try:
        data = pd.read_csv(data_file, sep='\t', encoding='utf-8')
        displacement_data = data.iloc[:, 1].values
        print(f"数据加载成功，数据点数: {len(displacement_data)}")
        print(f"数据范围: {displacement_data.min():.6f} ~ {displacement_data.max():.6f} μm")
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None, None, None
    
    # 3. 数据预处理
    print("\n步骤3: 数据预处理")
    window_size = 25
    train_points = min(1000, len(displacement_data) - window_size)
    train_data = displacement_data[:train_points + window_size]
    
    # 创建序列
    X, y = create_sequences(train_data, window_size)
    print(f"训练序列数量: {len(X)}")
    
    # 数据标准化
    scaler = MinMaxScaler(feature_range=(-1, 1))
    flat_data = train_data.reshape(-1, 1)
    scaler.fit(flat_data)
    
    # 标准化序列数据
    X_normalized = []
    for seq in X:
        norm_seq = scaler.transform(seq.reshape(-1, 1)).flatten()
        X_normalized.append(norm_seq)
    
    y_normalized = scaler.transform(y.reshape(-1, 1)).flatten()
    
    # 转换为张量
    X_tensor = torch.FloatTensor(X_normalized).unsqueeze(-1)
    y_tensor = torch.FloatTensor(y_normalized)
    
    # 4. 模型架构优化
    print("\n步骤4: 模型架构优化")
    if use_optimizations:
        arch_optimizer = ModelArchitectureOptimizer()
        
        # 比较不同架构
        print("比较不同模型架构...")
        arch_results = arch_optimizer.compare_model_architectures(
            X_tensor[:100], y_tensor[:100], 
            architectures=['basic_lstm', 'enhanced_lstm', 'attention_lstm']
        )
        
        # 选择最佳架构
        valid_archs = {k: v for k, v in arch_results.items() if 'loss' in v}
        if valid_archs:
            best_arch = min(valid_archs.keys(), key=lambda k: valid_archs[k]['loss'])
            print(f"选择最佳架构: {best_arch}")
            model = arch_optimizer.create_model(best_arch, input_size=1, output_size=1)
        else:
            print("架构比较失败，使用默认架构")
            model = EnhancedLSTM(input_size=1, hidden_size=128, num_layers=2)
    else:
        model = EnhancedLSTM(input_size=1, hidden_size=64, num_layers=1)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 5. 训练策略优化
    print("\n步骤5: 训练策略优化")
    if use_optimizations:
        strategy_optimizer = TrainingStrategyOptimizer()
        trainer = strategy_optimizer.create_trainer(model, strategy_name='advanced')
        
        # 创建数据加载器
        train_loader, val_loader = create_data_loaders(
            X_tensor, y_tensor, batch_size=32, val_split=0.2
        )
        
        # 训练模型
        print("开始高级训练...")
        history = trainer.train(train_loader, val_loader, epochs=100, verbose=True)
        
        print(f"训练完成，最终训练损失: {history['train_losses'][-1]:.6f}")
        print(f"最佳验证损失: {history['best_val_loss']:.6f}")
    else:
        # 基础训练
        criterion = nn.MSELoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
        
        model.train()
        for epoch in range(50):
            optimizer.zero_grad()
            outputs = model(X_tensor).squeeze()
            loss = criterion(outputs, y_tensor)
            loss.backward()
            optimizer.step()
            
            if epoch % 10 == 0:
                print(f'Epoch {epoch}: Loss = {loss.item():.6f}')
    
    model.eval()
    print("✅ 模型训练完成")
    
    return model, scaler, displacement_data


def test_prediction_accuracy(model, scaler, test_data, window_size=25, use_akf=True):
    """测试预测精度"""
    print("\n" + "=" * 60)
    print("测试预测精度")
    print("=" * 60)
    
    # 准备测试数据
    test_start = len(test_data) - 200  # 使用最后200个点进行测试
    test_sequence = test_data[test_start:]
    
    if len(test_sequence) < window_size + 50:
        print("测试数据不足，使用全部数据")
        test_sequence = test_data
        test_start = 0
    
    # 基础LSTM预测
    print("\n1. 基础LSTM预测")
    lstm_predictions = []
    actual_values = []
    
    for i in range(window_size, len(test_sequence)):
        # 准备输入序列
        input_seq = test_sequence[i-window_size:i]
        actual_val = test_sequence[i]
        
        # LSTM预测
        normalized_seq = scaler.transform(input_seq.reshape(-1, 1)).flatten()
        input_tensor = torch.FloatTensor(normalized_seq).unsqueeze(0).unsqueeze(-1)
        
        with torch.no_grad():
            normalized_pred = model(input_tensor).item()
        
        lstm_pred = scaler.inverse_transform([[normalized_pred]])[0, 0]
        
        lstm_predictions.append(lstm_pred)
        actual_values.append(actual_val)
    
    lstm_predictions = np.array(lstm_predictions)
    actual_values = np.array(actual_values)
    
    # 计算LSTM性能
    lstm_mae = np.mean(np.abs(lstm_predictions - actual_values))
    lstm_rmse = np.sqrt(np.mean((lstm_predictions - actual_values) ** 2))
    lstm_mape = np.mean(np.abs((lstm_predictions - actual_values) / actual_values)) * 100
    
    print(f"LSTM MAE: {lstm_mae:.6f} μm")
    print(f"LSTM RMSE: {lstm_rmse:.6f} μm")
    print(f"LSTM MAPE: {lstm_mape:.2f}%")
    
    # 2. AKF优化预测
    if use_akf:
        print("\n2. AKF优化预测")
        
        # 参数优化
        akf_optimizer = AKFParameterOptimizer()
        optimization_result = akf_optimizer.optimize_parameters(
            lstm_predictions, actual_values, actual_values,
            method='differential_evolution', max_evaluations=30
        )
        
        print("最优AKF参数:")
        best_params = optimization_result['best_params']
        for key, value in best_params.items():
            print(f"  {key}: {value:.2e}")
        
        # 使用优化参数创建AKF
        akf = OptimizedAdaptiveKalmanFilter(
            process_variance=best_params['process_variance'],
            measurement_variance=best_params['measurement_variance'],
            adaptation_rate=best_params['adaptation_rate'],
            adaptive_strategy='hybrid'
        )
        
        # AKF滤波
        akf_predictions = []
        for i, (lstm_pred, actual_val) in enumerate(zip(lstm_predictions, actual_values)):
            akf_pred = akf.predict_and_update(actual_val, lstm_pred, actual_val)
            akf_predictions.append(akf_pred)
        
        akf_predictions = np.array(akf_predictions)
        
        # 计算AKF性能
        akf_mae = np.mean(np.abs(akf_predictions - actual_values))
        akf_rmse = np.sqrt(np.mean((akf_predictions - actual_values) ** 2))
        akf_mape = np.mean(np.abs((akf_predictions - actual_values) / actual_values)) * 100
        
        print(f"AKF MAE: {akf_mae:.6f} μm")
        print(f"AKF RMSE: {akf_rmse:.6f} μm")
        print(f"AKF MAPE: {akf_mape:.2f}%")
        
        # 计算改进程度
        mae_improvement = (lstm_mae - akf_mae) / lstm_mae * 100
        rmse_improvement = (lstm_rmse - akf_rmse) / lstm_rmse * 100
        mape_improvement = (lstm_mape - akf_mape) / lstm_mape * 100
        
        print(f"\n📈 AKF改进效果:")
        print(f"MAE改进: {mae_improvement:.2f}%")
        print(f"RMSE改进: {rmse_improvement:.2f}%")
        print(f"MAPE改进: {mape_improvement:.2f}%")
        
        return {
            'lstm': {'mae': lstm_mae, 'rmse': lstm_rmse, 'mape': lstm_mape, 'predictions': lstm_predictions},
            'akf': {'mae': akf_mae, 'rmse': akf_rmse, 'mape': akf_mape, 'predictions': akf_predictions},
            'actual': actual_values,
            'improvements': {'mae': mae_improvement, 'rmse': rmse_improvement, 'mape': mape_improvement}
        }
    else:
        return {
            'lstm': {'mae': lstm_mae, 'rmse': lstm_rmse, 'mape': lstm_mape, 'predictions': lstm_predictions},
            'actual': actual_values
        }


def create_comparison_plots(results):
    """创建对比图表"""
    print("\n生成对比图表...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('预测精度优化效果对比', fontsize=16, fontweight='bold')
    
    actual = results['actual']
    lstm_pred = results['lstm']['predictions']
    time_steps = range(len(actual))
    
    # 第一张图：预测对比
    axes[0, 0].plot(time_steps, actual, 'b-', linewidth=2, label='真实值', alpha=0.8)
    axes[0, 0].plot(time_steps, lstm_pred, 'r--', linewidth=1.5, label='LSTM预测', alpha=0.7)
    
    if 'akf' in results:
        akf_pred = results['akf']['predictions']
        axes[0, 0].plot(time_steps, akf_pred, 'g:', linewidth=1.5, label='LSTM+AKF预测', alpha=0.7)
    
    axes[0, 0].set_xlabel('时间步')
    axes[0, 0].set_ylabel('位移 (μm)')
    axes[0, 0].set_title('预测值对比')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 第二张图：误差对比
    lstm_errors = np.abs(lstm_pred - actual)
    axes[0, 1].plot(time_steps, lstm_errors, 'r-', linewidth=1.5, label='LSTM误差', alpha=0.7)
    
    if 'akf' in results:
        akf_errors = np.abs(akf_pred - actual)
        axes[0, 1].plot(time_steps, akf_errors, 'g-', linewidth=1.5, label='LSTM+AKF误差', alpha=0.7)
    
    axes[0, 1].set_xlabel('时间步')
    axes[0, 1].set_ylabel('绝对误差 (μm)')
    axes[0, 1].set_title('预测误差对比')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 第三张图：误差分布
    axes[1, 0].hist(lstm_errors, bins=30, alpha=0.7, color='red', label='LSTM误差', density=True)
    
    if 'akf' in results:
        axes[1, 0].hist(akf_errors, bins=30, alpha=0.7, color='green', label='LSTM+AKF误差', density=True)
    
    axes[1, 0].set_xlabel('绝对误差 (μm)')
    axes[1, 0].set_ylabel('密度')
    axes[1, 0].set_title('误差分布')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 第四张图：性能指标对比
    metrics = ['MAE', 'RMSE', 'MAPE']
    lstm_values = [results['lstm']['mae'], results['lstm']['rmse'], results['lstm']['mape']]
    
    x_pos = np.arange(len(metrics))
    width = 0.35
    
    bars1 = axes[1, 1].bar(x_pos - width/2, lstm_values, width, label='LSTM', color='red', alpha=0.7)
    
    if 'akf' in results:
        akf_values = [results['akf']['mae'], results['akf']['rmse'], results['akf']['mape']]
        bars2 = axes[1, 1].bar(x_pos + width/2, akf_values, width, label='LSTM+AKF', color='green', alpha=0.7)
        
        # 添加改进百分比标注
        improvements = results['improvements']
        for i, (metric, improvement) in enumerate(zip(metrics, [improvements['mae'], improvements['rmse'], improvements['mape']])):
            axes[1, 1].text(i, max(lstm_values[i], akf_values[i]) * 1.1, f'+{improvement:.1f}%', 
                           ha='center', va='bottom', fontweight='bold', color='blue')
    
    axes[1, 1].set_xlabel('性能指标')
    axes[1, 1].set_ylabel('数值')
    axes[1, 1].set_title('性能指标对比')
    axes[1, 1].set_xticks(x_pos)
    axes[1, 1].set_xticklabels(metrics)
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('precision_improvement_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 对比图表已保存: precision_improvement_comparison.png")


def main():
    """主函数"""
    print("🚀 预测精度提升综合演示")
    print("=" * 60)
    
    # 检查数据文件
    import os
    if not os.path.exists('v1.txt'):
        print("❌ 找不到数据文件 v1.txt")
        print("请确保数据文件存在")
        return
    
    # 1. 训练优化后的模型
    model, scaler, test_data = train_optimized_model(use_optimizations=True)
    
    if model is None:
        print("❌ 模型训练失败")
        return
    
    # 2. 测试预测精度
    results = test_prediction_accuracy(model, scaler, test_data, use_akf=True)
    
    # 3. 生成对比图表
    create_comparison_plots(results)
    
    # 4. 输出总结
    print("\n" + "=" * 60)
    print("🎉 精度提升演示完成")
    print("=" * 60)
    
    print("\n📊 最终结果总结:")
    print(f"LSTM模型:")
    print(f"  MAE: {results['lstm']['mae']:.6f} μm")
    print(f"  RMSE: {results['lstm']['rmse']:.6f} μm")
    print(f"  MAPE: {results['lstm']['mape']:.2f}%")
    
    if 'akf' in results:
        print(f"\nLSTM+AKF优化:")
        print(f"  MAE: {results['akf']['mae']:.6f} μm")
        print(f"  RMSE: {results['akf']['rmse']:.6f} μm")
        print(f"  MAPE: {results['akf']['mape']:.2f}%")
        
        print(f"\n🎯 总体改进效果:")
        print(f"  MAE改进: {results['improvements']['mae']:.2f}%")
        print(f"  RMSE改进: {results['improvements']['rmse']:.2f}%")
        print(f"  MAPE改进: {results['improvements']['mape']:.2f}%")
    
    print("\n✅ 所有优化模块已成功应用！")


if __name__ == "__main__":
    main()
