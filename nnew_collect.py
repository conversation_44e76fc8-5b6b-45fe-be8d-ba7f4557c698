import ctypes
from ctypes import *
import time
import os
import queue
import matplotlib.pyplot as plt

class DSADataCollector:
    def __init__(self, dll_path):
        """初始化数据收集器"""
        if not os.path.exists(dll_path):
            raise FileNotFoundError(dll_path)
        self.sdk = ctypes.cdll.LoadLibrary(dll_path)
        
        # 定义回调函数类型
        self.CALLBACK = CFUNCTYPE(None, c_int, c_int, c_int, c_int, POINTER(c_float), c_int)
        self._callback_func = self.CALLBACK(self._data_callback)
        
        # 数据缓冲区
        self.training_data = []  # 原始训练数据
        self.start_time = None
        self.is_running = True
        
        # 实时数据流
        self.real_time_queue = queue.Queue(maxsize=10000)
        self.enable_real_time_stream = True
        
        # SDK 枚举
        self.OutputFilter = {'of1k':0x00,'of2k':0x01,'of10k':0x02,'of20k':0x03}
        self.OutDataType = {'odtVelocity':0x01,'odtDisplacement':0x02,'odtAll':0x03,'odtNoOutput':0x04}
        self.VelocityRange = {'vr1':0x01}
        self.DisplacementRange = {'dr1':0x01}
        self.DeviceType = {'DT3M':0x01}
        self.LaserWaveLength = {'LW_632_8':0x01}

    def _data_callback(self, dataType, sRate, vRange, dRange, data_ptr, dataLen):
        """SDK 数据回调"""
        if not self.is_running:
            return

        # 将指针转换为数组
        array_type = c_float * dataLen
        data = cast(data_ptr, POINTER(array_type)).contents
        values = list(data)

        # 初始化开始时间
        current_time = time.time()
        if self.start_time is None:
            self.start_time = current_time
            print("开始收集数据...")

        # 检查运行时间（收集前3秒数据）
        elapsed_time = current_time - self.start_time
        if elapsed_time >= 3.0:
            self.is_running = False
            print(f"\n已运行3秒，停止收集")
            return

        # SDK 实际采样率
        effective_sRate = sRate if sRate > 0 else 10000  # 默认 10000Hz

        # 收集训练数据并放入实时队列
        for i, value in enumerate(values):
            timestamp_ms = (elapsed_time + i / effective_sRate) * 1000  # 相对时间, ms
            self.training_data.append({'timestamp_ms': timestamp_ms, 'value': value})
            if self.enable_real_time_stream:
                self.real_time_queue.put_nowait({'timestamp_ms': timestamp_ms, 'value': value})

    def initialize_sdk(self):
        """初始化 SDK"""
        print("初始化SDK...")
        result = self.sdk.initialize()
        if result != 0:
            print(f"初始化失败: {result}")
            return False
        
        self.sdk.setBindInfo(b"0.0.0.0", 62618)
        self.sdk.setBoardcastInfo(b"255.255.255.255", 63082)
        self.sdk.setDeviceType(self.DeviceType['DT3M'])
        self.sdk.setDeviceId(0)
        self.sdk.setLaserWaveLength(self.LaserWaveLength['LW_632_8'])
        self.sdk.setDataCallBack(self._callback_func)
        self.sdk.setDataLength(256)  # 每次回调256点
        self.sdk.setOutputFilter(self.OutputFilter['of10k'])  # 设置为10000Hz采样
        self.sdk.setVelocityRange(self.VelocityRange['vr1'])
        self.sdk.setDisplacementRange(self.DisplacementRange['dr1'])
        self.sdk.setOutDataType(self.OutDataType['odtDisplacement'])
        
        print("SDK初始化完成")
        return True

    def start_collection(self):
        """启动数据采集"""
        print("启动数据采集...")
        result = self.sdk.start()
        if result != 0:
            print(f"启动失败: {result}")
            return False
        return True

    def stop_collection(self):
        """停止采集"""
        self.sdk.stop()
        self.sdk.unInitialize()
        print("采集停止，SDK已反初始化")

    def save_training_data(self):
        """保存训练数据到 v1.txt，均匀从每10个点取1个"""
        filename = "v1.txt"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("时间[ms]\t位移[μm]\n")
            total_points = len(self.training_data)
            step = 10  # 每10个点取1个
            for i in range(0, total_points, step):
                point = self.training_data[i]
                f.write(f"{point['timestamp_ms']:.3f}\t{point['value']:.6f}\n")
        print(f"训练数据已保存到 {filename}, 共 {total_points // step} 个数据点")

    def plot_training_data(self):
        """绘制v1.txt中的位移曲线"""
        timestamps = [d['timestamp_ms'] for d in self.training_data[::10]]  # 每10个取1
        values = [d['value'] for d in self.training_data[::10]]
        
        plt.figure(figsize=(10,5))
        plt.plot(timestamps, values, color='blue', linewidth=1)
        plt.title("位移随时间变化曲线")
        plt.xlabel("时间 [ms]")
        plt.ylabel("位移 [μm]")
        plt.grid(True)
        plt.tight_layout()
        plt.show()

    def run(self):
        """运行收集"""
        if not self.initialize_sdk():
            return False
        if not self.start_collection():
            return False
        
        print("\n开始收集前3秒数据...")
        try:
            while self.is_running:
                time.sleep(0.05)
        except KeyboardInterrupt:
            print("用户中断采集")
            self.is_running = False
        finally:
            self.stop_collection()
            self.save_training_data()
            self.plot_training_data()  # 绘图
        print("数据收集完成")
        return True


def main():
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    if not os.path.exists(dll_path):
        print(f"错误：找不到DLL文件 {dll_path}")
        return

    collector = DSADataCollector(dll_path)
    collector.run()


if __name__ == "__main__":
    main()
