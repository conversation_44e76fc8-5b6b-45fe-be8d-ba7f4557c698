"""
诊断数据源的实际读取速率
"""

import asyncio
import time
import sys
sys.path.insert(0, '.')

from real_time_data_bridge import DSADataSource

async def diagnose_data_source():
    """诊断数据源的实际读取速率"""
    
    print("="*70)
    print("数据源诊断工具")
    print("="*70)
    
    # DLL 路径
    dll_path = r"SDK发布20200723/x64/DSANetSDK.dll"
    
    # 创建数据源
    print("\n📊 初始化数据源...")
    displacement_source = DSADataSource(dll_path, 'displacement')
    
    # 启动数据收集
    print("🚀 启动数据收集...")
    if not displacement_source.start_collection():
        print("❌ 启动失败")
        return
    
    # 等待数据稳定
    print("⏳ 等待数据稳定（2秒）...")
    await asyncio.sleep(2.0)
    
    # 测试不同的读取策略
    strategies = [
        ("单次读取（1ms超时）", 1, 0.001),
        ("单次读取（10ms超时）", 1, 0.01),
        ("单次读取（100ms超时）", 1, 0.1),
        ("10次读取（1ms超时）", 10, 0.001),
        ("100次读取（1ms超时）", 100, 0.001),
    ]
    
    for strategy_name, read_count, timeout in strategies:
        print(f"\n{'='*70}")
        print(f"测试策略: {strategy_name}")
        print(f"  - 读取次数: {read_count}")
        print(f"  - 超时时间: {timeout*1000:.1f}ms")
        print(f"{'='*70}")
        
        # 测试 10 秒
        test_duration = 10
        start_time = time.time()
        data_count = 0
        read_attempts = 0
        
        while time.time() - start_time < test_duration:
            for _ in range(read_count):
                read_attempts += 1
                # 直接调用底层方法
                data = displacement_source.collector.get_real_time_data_by_type(
                    'displacement', 
                    timeout=timeout
                )
                if data is not None:
                    data_count += 1
            
            # 短暂让出控制权
            await asyncio.sleep(0.00001)
        
        elapsed = time.time() - start_time
        actual_rate = data_count / elapsed
        read_efficiency = (data_count / read_attempts * 100) if read_attempts > 0 else 0
        
        print(f"\n📈 结果:")
        print(f"  - 总耗时: {elapsed:.2f}s")
        print(f"  - 读取尝试: {read_attempts}")
        print(f"  - 成功读取: {data_count}")
        print(f"  - 实际采样率: {actual_rate:.1f} Hz")
        print(f"  - 读取效率: {read_efficiency:.1f}%")
        print(f"  - 队列大小: {displacement_source.collector.get_real_time_queue_size()}")
    
    # 停止数据收集
    print(f"\n{'='*70}")
    print("停止数据收集...")
    displacement_source.stop_collection()
    
    print("\n✅ 诊断完成！")
    print("\n💡 分析:")
    print("  1. 如果采样率 < 100 Hz，说明数据源本身速率有限")
    print("  2. 如果读取效率 < 50%，说明队列中数据不足")
    print("  3. 如果队列大小持续增长，说明读取速度跟不上数据到达速度")

if __name__ == "__main__":
    asyncio.run(diagnose_data_source())

