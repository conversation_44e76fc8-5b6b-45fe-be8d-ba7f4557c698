import ctypes
from ctypes import *
import time
import os
import asyncio
import queue
import threading

class DSADataCollector:
    def __init__(self, dll_path):
        """初始化数据收集器"""
        self.sdk = ctypes.cdll.LoadLibrary(dll_path)
        
        # 定义回调函数类型
        self.CALLBACK = CFUNCTYPE(None, c_int, c_int, c_int, c_int, POINTER(c_float), c_int)
        self._callback_func = self.CALLBACK(self._data_callback)
        
        # 数据收集相关变量
        self.displacement_buffer = []  # 位移数据缓冲区
        self.velocity_buffer = []      # 速度数据缓冲区
        self.start_time = None
        self.last_output_time = 0
        self.output_count = 0
        self.file_count = 0  # 实际生成的文件数量
        self.target_points_per_second = 2000  # 每秒目标2000个点
        self.total_duration = 5  # 5秒足够收集1000个1000Hz数据点
        self.is_running = True
        self.current_data_type = None  # 当前数据类型
        self.continuous_mode = False  # 连续模式：训练完成后继续运行
        # 是否生成每秒位移/速度文件（默认不生成，仅用于实时流）
        self.enable_per_second_file_output = False
        
        # 枚举定义
        self.OutputFilter = {'of1k': 0x00, 'of2k': 0x01, 'of10k': 0x02, 'of20k': 0x03}  # 添加1000Hz选项
        self.OutDataType = {'odtVelocity': 0x01, 'odtDisplacement': 0x02, 'odtAll': 0x03, 'odtNoOutput': 0x04}
        self.VelocityRange = {'vr1': 0x01}
        self.DisplacementRange = {'dr1': 0x01}
        self.DeviceType = {'DT3M': 0x01}
        self.LaserWaveLength = {'LW_632_8': 0x01}

        # 数据交替标志（用于处理odtAll模式）
        self.data_toggle = False  # False=位移, True=速度

        # 实时数据流支持
        self.real_time_queue = queue.Queue(maxsize=10000)  # 实时数据队列
        self.enable_real_time_stream = False  # 是否启用实时数据流
        # 按类型分离的实时队列，避免不同读者互相抢占/丢弃
        self.real_time_queue_displacement = queue.Queue(maxsize=10000)
        self.real_time_queue_velocity = queue.Queue(maxsize=10000)

        # 训练数据收集
        self.training_data = []  # 训练数据缓冲区
        self.training_data_collected = False  # 是否已收集训练数据
        self.training_target_count = 1000  # 训练数据目标数量（修改为1000个点）

    def _data_callback(self, dataType, sRate, vRange, dRange, data_ptr, dataLen):
        """数据回调函数 - 分别处理位移和速度数据"""
        if not self.is_running:
            return

        # 转换数据
        array_type = c_float * dataLen
        data = cast(data_ptr, POINTER(array_type)).contents
        values = list(data)

        current_time = time.time()

        # 初始化时间
        if self.start_time is None:
            self.start_time = current_time
            self.last_output_time = current_time
            print("开始收集数据...")
            print(f"采样方式：每秒均匀选择{self.target_points_per_second}个数据点")
            print(f"总共运行{self.total_duration}秒")

        # 检查运行时间（如果不是连续模式）
        elapsed_time = current_time - self.start_time
        if not self.continuous_mode and elapsed_time >= self.total_duration:
            self.is_running = False
            print(f"\n已运行{self.total_duration}秒，停止收集")
            return
        elif self.continuous_mode and self.training_data_collected and elapsed_time >= self.total_duration:
            # 连续模式下，训练数据收集完成后继续运行
            print(f"\n训练数据收集完成，继续运行进行预测...")
            # 重置计时器，开始预测阶段
            self.start_time = current_time
            self.total_duration = 10  # 预测阶段运行10秒

        # 显示数据类型信息（仅在前几次回调时显示）
        if elapsed_time < 2.0:  # 前2秒显示详细信息
            data_type_name = "未知"
            if dataType == self.OutDataType['odtDisplacement']:
                data_type_name = "位移"
            elif dataType == self.OutDataType['odtVelocity']:
                data_type_name = "速度"
            elif dataType == self.OutDataType['odtAll']:
                data_type_name = "全部"
            print(f"接收到{data_type_name}数据: {dataLen}个点, 数据类型码: {dataType}")

        # 根据数据类型添加到对应缓冲区
        # 修复采样率为0的问题，使用默认65Hz
        effective_sRate = sRate if sRate > 0 else 65

        for i, value in enumerate(values):
            data_point = {
                'timestamp': current_time + (i / effective_sRate),
                'value': value
            }

            if dataType == self.OutDataType['odtDisplacement']:
                self.displacement_buffer.append(data_point)
                # 收集训练数据（前200个位移点）
                if not self.training_data_collected and len(self.training_data) < self.training_target_count:
                    self.training_data.append(data_point['value'])
                    if len(self.training_data) >= self.training_target_count:
                        self._save_training_data()
                        self.training_data_collected = True
                # 添加到实时数据流（仅位移数据）
                if self.enable_real_time_stream:
                    self._add_to_real_time_stream(data_point, 'displacement')
            elif dataType == self.OutDataType['odtVelocity']:
                self.velocity_buffer.append(data_point)
                # 添加到实时数据流（仅速度数据）
                if self.enable_real_time_stream:
                    self._add_to_real_time_stream(data_point, 'velocity')
            elif dataType == self.OutDataType['odtAll']:
                # 在odtAll模式下，数据可能交替包含位移和速度
                # 或者数据包中前半部分是位移，后半部分是速度
                # 这里假设数据交替出现
                if self.data_toggle:
                    self.velocity_buffer.append(data_point)
                    if self.enable_real_time_stream:
                        self._add_to_real_time_stream(data_point, 'velocity')
                else:
                    self.displacement_buffer.append(data_point)
                    if self.enable_real_time_stream:
                        self._add_to_real_time_stream(data_point, 'displacement')
                self.data_toggle = not self.data_toggle

        # 每秒输出一次
        if current_time - self.last_output_time >= 1.0:
            if self.enable_per_second_file_output:
                self._save_second_data()
            self.last_output_time = current_time

    def _add_to_real_time_stream(self, data_point, data_type):
        """添加数据到实时流队列"""
        try:
            stream_data = {
                'timestamp': data_point['timestamp'],
                'value': data_point['value'],
                'type': data_type
            }
            self.real_time_queue.put_nowait(stream_data)
            # 额外：按类型放入对应队列
            if data_type == 'displacement':
                try:
                    self.real_time_queue_displacement.put_nowait(stream_data)
                except queue.Full:
                    try:
                        self.real_time_queue_displacement.get_nowait()
                        self.real_time_queue_displacement.put_nowait(stream_data)
                    except queue.Empty:
                        pass
            elif data_type == 'velocity':
                try:
                    self.real_time_queue_velocity.put_nowait(stream_data)
                except queue.Full:
                    try:
                        self.real_time_queue_velocity.get_nowait()
                        self.real_time_queue_velocity.put_nowait(stream_data)
                    except queue.Empty:
                        pass
        except queue.Full:
            # 队列满时丢弃最旧的数据
            try:
                self.real_time_queue.get_nowait()
                self.real_time_queue.put_nowait(stream_data)
            except queue.Empty:
                pass

    def enable_real_time_streaming(self):
        """启用实时数据流"""
        self.enable_real_time_stream = True
        print("实时数据流已启用")

    def disable_real_time_streaming(self):
        """禁用实时数据流"""
        self.enable_real_time_stream = False
        print("实时数据流已禁用")

    def enable_continuous_mode(self):
        """启用连续模式：训练完成后继续运行进行预测"""
        self.continuous_mode = True
        print("连续模式已启用：训练完成后将继续运行进行预测")

    def get_real_time_data(self, timeout=0.001):
        """获取实时数据（非阻塞）"""
        try:
            return self.real_time_queue.get(timeout=timeout)
        except queue.Empty:
            return None

    def get_real_time_data_by_type(self, data_type, timeout=0.001):
        """按类型获取实时数据（非阻塞，不会消耗其他类型队列）"""
        try:
            if data_type == 'displacement':
                return self.real_time_queue_displacement.get(timeout=timeout)
            elif data_type == 'velocity':
                return self.real_time_queue_velocity.get(timeout=timeout)
            else:
                return None
        except queue.Empty:
            return None

    def get_real_time_queue_size(self):
        """获取实时数据队列大小"""
        return self.real_time_queue.qsize()

    def _save_training_data(self):
        """保存训练数据到v1.txt文件"""
        filename = "v1.txt"
        print(f"\n💾 保存训练数据到 {filename}...")

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("时间[ms]\t1-5-a1-(位移)-时域[μm]\n")  # 写入表头

            # 生成时间戳（2000Hz采样频率）
            time_interval = 1000.0 / 2000.0  # 0.5ms间隔

            for i, value in enumerate(self.training_data):
                timestamp = i * time_interval
                f.write(f"{timestamp:.3f}\t{value:.6f}\n")

        print(f"✅ 训练数据已保存: {len(self.training_data)} 个位移数据点")
        print(f"   文件: {filename}")
        print(f"   数据范围: {min(self.training_data):.6f} ~ {max(self.training_data):.6f} μm")

    def get_training_data_status(self):
        """获取训练数据收集状态"""
        return {
            'collected': self.training_data_collected,
            'count': len(self.training_data),
            'target': self.training_target_count,
            'progress': len(self.training_data) / self.training_target_count * 100
        }

    def _save_second_data(self):
        """保存一秒的位移和速度数据到文件"""
        self.output_count += 1

        print(f"\n--- 第{self.output_count}秒数据处理 ---")
        print(f"位移缓冲区数据点数: {len(self.displacement_buffer)}")
        print(f"速度缓冲区数据点数: {len(self.velocity_buffer)}")

        # 保存位移数据
        if len(self.displacement_buffer) > 0:
            self._save_data_file(self.displacement_buffer, "displacement", "um")
        else:
            print(f"⚠️ 第{self.output_count}秒: 位移缓冲区为空，未生成位移文件")

        # 保存速度数据
        if len(self.velocity_buffer) > 0:
            self._save_data_file(self.velocity_buffer, "velocity", "mm_per_s")
        else:
            print(f"⚠️ 第{self.output_count}秒: 速度缓冲区为空，未生成速度文件")

        # 清空缓冲区
        self.displacement_buffer.clear()
        self.velocity_buffer.clear()

    def _save_data_file(self, data_buffer, data_type, unit):
        """保存指定类型的数据到文件 - 每秒均匀选择4000个点"""
        if len(data_buffer) == 0:
            return

        # 排序数据（按时间戳）
        sorted_data = sorted(data_buffer, key=lambda x: x['timestamp'])

        # 均匀选择4000个数据点
        total_points = len(sorted_data)
        if total_points >= self.target_points_per_second:
            # 均匀选择4000个点
            step = total_points / self.target_points_per_second
            indices = [int(i * step) for i in range(self.target_points_per_second)]
            selected_data = [sorted_data[i] for i in indices]
        else:
            # 如果数据点不够4000个，使用所有数据
            selected_data = sorted_data.copy()

        # 计算时间跨度用于文件头信息
        start_time = sorted_data[0]['timestamp']
        end_time = sorted_data[-1]['timestamp']
        duration = end_time - start_time

        # 生成文件名
        filename = f"second_{self.output_count}_{data_type}_data.txt"

        # 保存文件
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"# 第{self.output_count}秒{data_type}数据\n")
            f.write(f"# 数据类型: {data_type}\n")
            f.write(f"# 单位: {unit}\n")
            f.write(f"# 采样方式: 每秒均匀选择{self.target_points_per_second}个点\n")
            f.write(f"# 选择的数据点数: {len(selected_data)}\n")
            f.write(f"# 原始数据点数: {len(sorted_data)}\n")
            f.write(f"# 时间跨度: {duration*1000:.1f} 毫秒\n")
            f.write(f"# 格式: 时间戳(秒,6位小数) 数值({unit})\n")
            f.write("# ----------------------------------------\n")

            for point in selected_data:
                f.write(f"{point['timestamp']:.6f} {point['value']:.6f}\n")

        print(f"✓ 第{self.output_count}秒: {len(selected_data)}个{data_type}数据点 (均匀选择) → {filename}")
        self.file_count += 1  # 增加文件计数

    def initialize_sdk(self):
        """初始化SDK"""
        print("初始化SDK...")
        result = self.sdk.initialize()
        if result != 0:
            print(f"初始化失败: {result}")
            return False
        
        # 网络配置
        self.sdk.setBindInfo(b"0.0.0.0", 62618)
        self.sdk.setBoardcastInfo(b"255.255.255.255", 63082)
        
        # 设备配置
        self.sdk.setDeviceType(self.DeviceType['DT3M'])
        self.sdk.setDeviceId(0)
        self.sdk.setLaserWaveLength(self.LaserWaveLength['LW_632_8'])
        
        # 数据配置
        self.sdk.setDataCallBack(self._callback_func)
        self.sdk.setDataLength(256)  # 适合1000Hz采样的数据包大小

        # 测量配置 - 设置为2kHz采样频率（更稳定的选择）
        self.sdk.setOutputFilter(self.OutputFilter['of2k'])
        self.sdk.setVelocityRange(self.VelocityRange['vr1'])
        self.sdk.setDisplacementRange(self.DisplacementRange['dr1'])
        
        # 设置输出所有数据（位移和速度）
        self.sdk.setOutDataType(self.OutDataType['odtNoOutput'])
        time.sleep(0.05)
        self.sdk.setOutDataType(self.OutDataType['odtAll'])  # 输出位移和速度数据
        
        print("SDK初始化完成")
        return True

    def start_collection(self):
        """开始数据收集"""
        print("启动数据收集...")
        result = self.sdk.start()
        if result != 0:
            print(f"启动失败: {result}")
            return False
        
        print("数据收集已启动")
        return True

    def run(self):
        """运行数据收集"""
        if not self.initialize_sdk():
            return False
        
        if not self.start_collection():
            return False
        
        print("\n" + "="*60)
        if self.enable_per_second_file_output:
            print("开始3秒数据收集，每秒输出位移和速度数据文件")
            print("采样方式：每秒均匀选择4000个数据点")
        else:
            print("开始数据收集（仅实时队列，不生成每秒数据文件）")
        print("="*60)
        
        try:
            if self.continuous_mode:
                # 连续模式：一直运行，直到外部停止
                print("连续模式运行中：将持续采集实时数据（仅队列，不限3.2秒）")
                while self.is_running:
                    time.sleep(0.05)
            else:
                # 非连续模式：运行3.2秒，留一点余量
                start = time.time()
                while time.time() - start < 3.2 and self.is_running:
                    time.sleep(0.1)
            
            # 处理最后的数据
            if self.enable_per_second_file_output:
                if len(self.displacement_buffer) > 0 or len(self.velocity_buffer) > 0:
                    self._save_second_data()
                
        except KeyboardInterrupt:
            print("\n用户中断...")
        finally:
            self.sdk.stop()
            self.sdk.unInitialize()
        
        print("\n" + "="*60)
        print(f"数据收集完成！")
        if self.enable_per_second_file_output:
            print(f"处理了 {self.output_count} 秒的数据")
            print(f"实际生成 {self.file_count} 个文件")
            print(f"预期生成 {self.output_count * 2} 个文件 (每秒位移+速度)")
            if self.file_count < self.output_count * 2:
                print(f"⚠️ 缺少 {self.output_count * 2 - self.file_count} 个文件，可能是某些时刻缓冲区为空")
            print("文件格式: second_X_displacement_data.txt 和 second_X_velocity_data.txt")
        else:
            print("本次运行未启用每秒文件输出，仅通过实时队列向上层提供数据。")
        print("="*60)
        
        return True


def main():
    """主函数"""
    print("DSANet 3秒数据收集器")
    print("功能：每秒均匀选择4000个数据点，总共3秒")
    print("输出：每秒生成2个文件（位移文件 + 速度文件）")
    print("-" * 60)
    
    # 检查DLL文件
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    if not os.path.exists(dll_path):
        print(f"错误：找不到DLL文件 {dll_path}")
        return
    
    # 创建收集器并运行
    collector = DSADataCollector(dll_path)
    success = collector.run()
    
    if success:
        print("\n程序正常结束")
    else:
        print("\n程序异常结束")


if __name__ == "__main__":
    main()
