"""
简化的实时数据输出程序
直接输出5秒的位移和速度数据并绘图
"""

import asyncio
import time
import numpy as np
import matplotlib.pyplot as plt
import os
from collections import deque
import warnings

# 导入数据收集模块
from real_time_data_bridge import DSADataSource, RealTimeVisualizationBridge

warnings.filterwarnings("ignore")

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.family'] = 'sans-serif'
    print("中文字体设置成功: SimHei")
except:
    plt.rcParams['axes.unicode_minus'] = False
    print("使用默认字体")


async def collect_real_time_data_ultra_aggressive(displacement_source, velocity_source, duration_seconds=5.0):
    """超激进的数据收集策略 - 直接访问底层队列"""
    print(f"🚀 开始超激进模式收集 {duration_seconds:.1f}s 的实时数据...")

    start_time = time.time()
    data_records = []
    sample_count = 0
    read_attempts = 0
    successful_reads = 0

    # 直接访问底层收集器
    displacement_collector = displacement_source.collector
    velocity_collector = velocity_source.collector

    while True:
        elapsed = time.time() - start_time
        if elapsed >= duration_seconds:
            break

        # 超高频读取 - 直接从队列读取，无超时
        displacement_data = None
        velocity_data = None

        # 连续尝试读取多次
        for _ in range(50):  # 每次循环尝试50次
            read_attempts += 1

            # 直接调用底层方法，无超时
            disp_raw = displacement_collector.get_real_time_data_by_type('displacement', timeout=0)
            if disp_raw is not None:
                displacement_data = disp_raw['value']
                successful_reads += 1

            vel_raw = velocity_collector.get_real_time_data_by_type('velocity', timeout=0)
            if vel_raw is not None:
                velocity_data = vel_raw['value']
                successful_reads += 1

            # 如果有位移数据就立即记录
            if displacement_data is not None:
                record = {
                    'time': elapsed,
                    'displacement': displacement_data,
                    'velocity': velocity_data if velocity_data is not None else 0.0
                }
                data_records.append(record)
                sample_count += 1

                # 每50个样本显示一次进度
                if sample_count % 50 == 0:
                    progress = (elapsed / duration_seconds) * 100
                    actual_rate = sample_count / elapsed if elapsed > 0 else 0
                    read_success_rate = (successful_reads / read_attempts * 100) if read_attempts > 0 else 0
                    queue_size = displacement_collector.get_real_time_queue_size()
                    print(f"  📊 [超激进] 已收集 {sample_count} 个数据点，进度: {progress:.1f}%，速率: {actual_rate:.1f} Hz，成功率: {read_success_rate:.1f}%，队列: {queue_size}")

                displacement_data = None  # 重置，避免重复记录
                break

        # 无等待，最大化读取频率
        await asyncio.sleep(0)  # 仅让出CPU控制权

    actual_duration = time.time() - start_time
    actual_rate = len(data_records) / actual_duration if actual_duration > 0 else 0
    read_success_rate = (successful_reads / read_attempts * 100) if read_attempts > 0 else 0

    print(f"✅ 超激进模式数据收集完成: {len(data_records)} 个数据点, 实际速率: {actual_rate:.1f} Hz")
    print(f"📊 读取统计: 总尝试 {read_attempts} 次, 成功 {successful_reads} 次, 成功率: {read_success_rate:.1f}%")

    return data_records


async def collect_real_time_data_aggressive(displacement_source, velocity_source, duration_seconds=5.0):
    """激进的数据收集策略 - 尽可能快地收集数据"""
    print(f"🚀 开始激进模式收集 {duration_seconds:.1f}s 的实时数据...")

    start_time = time.time()
    data_records = []
    sample_count = 0

    while True:
        elapsed = time.time() - start_time
        if elapsed >= duration_seconds:
            break

        # 读取位移数据
        displacement_data = await displacement_source.read_data()
        velocity_data = await velocity_source.read_data()

        # 只要有位移数据就记录
        if displacement_data is not None:
            record = {
                'time': elapsed,
                'displacement': displacement_data,
                'velocity': velocity_data if velocity_data is not None else 0.0
            }
            data_records.append(record)
            sample_count += 1

            # 每100个样本显示一次进度
            if sample_count % 100 == 0:
                progress = (elapsed / duration_seconds) * 100
                actual_rate = sample_count / elapsed if elapsed > 0 else 0
                print(f"  📊 [激进模式] 已收集 {sample_count} 个数据点，进度: {progress:.1f}%，当前速率: {actual_rate:.1f} Hz")

        # 极短等待
        await asyncio.sleep(0.0001)  # 0.1ms

    actual_duration = time.time() - start_time
    actual_rate = len(data_records) / actual_duration if actual_duration > 0 else 0

    print(f"✅ 激进模式数据收集完成: {len(data_records)} 个数据点, 实际速率: {actual_rate:.1f} Hz")

    return data_records


async def collect_real_time_data(displacement_source, velocity_source, duration_seconds=5.0):
    """收集指定时长的实时位移和速度数据"""
    print(f"🚀 开始收集 {duration_seconds:.1f}s 的实时位移和速度数据...")

    start_time = time.time()
    data_records = []
    last_displacement = None
    last_velocity = None

    # 目标采样率 (Hz)
    target_sampling_rate = 200  # 200 Hz
    min_sample_interval = 1.0 / target_sampling_rate
    last_sample_time = start_time

    sample_count = 0
    read_attempts = 0
    successful_reads = 0
    
    while True:
        elapsed = time.time() - start_time
        if elapsed >= duration_seconds:
            break

        # 高频读取数据，尽可能获取最新数据
        for _ in range(20):  # 增加读取次数
            read_attempts += 1

            # 读取位移数据
            displacement_data = await displacement_source.read_data()
            if displacement_data is not None:
                last_displacement = displacement_data
                successful_reads += 1

            # 读取速度数据
            velocity_data = await velocity_source.read_data()
            if velocity_data is not None:
                last_velocity = velocity_data
                successful_reads += 1

            # 极短等待，让出CPU
            await asyncio.sleep(0.000001)  # 1μs

        # 检查是否到了采样时间
        current_time = time.time()
        if (current_time - last_sample_time) >= min_sample_interval:
            if last_displacement is not None:
                record = {
                    'time': elapsed,
                    'displacement': last_displacement,
                    'velocity': last_velocity if last_velocity is not None else 0.0
                }
                data_records.append(record)
                last_sample_time = current_time
                sample_count += 1

                # 每100个样本显示一次进度
                if sample_count % 100 == 0:
                    progress = (elapsed / duration_seconds) * 100
                    actual_rate = sample_count / elapsed if elapsed > 0 else 0
                    read_success_rate = (successful_reads / read_attempts * 100) if read_attempts > 0 else 0
                    print(f"  📊 已收集 {sample_count} 个数据点，进度: {progress:.1f}%，当前速率: {actual_rate:.1f} Hz，读取成功率: {read_success_rate:.1f}%")

        # 减少等待时间，提高采样频率
        await asyncio.sleep(0.001)  # 1ms，约1000Hz的检查频率
    
    actual_duration = time.time() - start_time
    actual_rate = len(data_records) / actual_duration if actual_duration > 0 else 0
    read_success_rate = (successful_reads / read_attempts * 100) if read_attempts > 0 else 0

    print(f"✅ 数据收集完成: {len(data_records)} 个数据点, 实际速率: {actual_rate:.1f} Hz")
    print(f"📊 读取统计: 总尝试 {read_attempts} 次, 成功 {successful_reads} 次, 成功率: {read_success_rate:.1f}%")

    return data_records


def save_data_to_file(data_records, filename="output_data.txt"):
    """保存数据到文件"""
    print(f"💾 保存数据到文件: {filename}")
    
    with open(filename, 'w', encoding='utf-8') as f:
        # 写入表头
        f.write("时间[s]\t位移[μm]\t速度[μm/s]\n")
        
        # 写入数据
        for record in data_records:
            f.write(f"{record['time']:.3f}\t"
                   f"{record['displacement']:.6f}\t"
                   f"{record['velocity']:.6f}\n")
    
    # 统计信息
    if data_records:
        displacements = [r['displacement'] for r in data_records]
        velocities = [r['velocity'] for r in data_records]
        
        print(f"✅ 数据已保存到 {filename}")
        print(f"   📊 总数据点数: {len(data_records)}")
        print(f"   ⏱️  时间范围: 0.000 ~ {data_records[-1]['time']:.3f} 秒")
        print(f"   📍 位移范围: {min(displacements):.6f} ~ {max(displacements):.6f} μm")
        print(f"   🚀 速度范围: {min(velocities):.6f} ~ {max(velocities):.6f} μm/s")
    
    return filename


def create_visualization(data_records, save_prefix="output"):
    """创建可视化图表"""
    print("📊 生成可视化图表...")
    
    if not data_records:
        print("❌ 没有数据可以绘图")
        return
    
    # 提取数据
    times = np.array([r['time'] for r in data_records])
    displacements = np.array([r['displacement'] for r in data_records])
    velocities = np.array([r['velocity'] for r in data_records])
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('DSA实时数据采集结果 (5秒)', fontsize=16, fontweight='bold')
    
    # 第一张图：位移随时间变化
    axes[0, 0].plot(times, displacements, 'b-', linewidth=1.5, label='位移数据', alpha=0.8)
    axes[0, 0].set_xlabel('时间 (s)')
    axes[0, 0].set_ylabel('位移 (μm)')
    axes[0, 0].set_title('位移随时间变化')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 添加统计信息
    disp_mean = np.mean(displacements)
    disp_std = np.std(displacements)
    disp_range = np.max(displacements) - np.min(displacements)
    
    axes[0, 0].text(0.02, 0.98, f'均值: {disp_mean:.6f}μm\n标准差: {disp_std:.6f}μm\n范围: {disp_range:.6f}μm',
                    transform=axes[0, 0].transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 第二张图：速度随时间变化
    axes[0, 1].plot(times, velocities, 'r-', linewidth=1.5, label='速度数据', alpha=0.8)
    axes[0, 1].set_xlabel('时间 (s)')
    axes[0, 1].set_ylabel('速度 (μm/s)')
    axes[0, 1].set_title('速度随时间变化')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 添加统计信息
    vel_mean = np.mean(velocities)
    vel_std = np.std(velocities)
    vel_range = np.max(velocities) - np.min(velocities)
    
    axes[0, 1].text(0.02, 0.98, f'均值: {vel_mean:.6f}μm/s\n标准差: {vel_std:.6f}μm/s\n范围: {vel_range:.6f}μm/s',
                    transform=axes[0, 1].transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 第三张图：位移分布直方图
    axes[1, 0].hist(displacements, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    axes[1, 0].axvline(x=disp_mean, color='red', linestyle='--', linewidth=2,
                       label=f'均值: {disp_mean:.6f}μm')
    axes[1, 0].set_xlabel('位移 (μm)')
    axes[1, 0].set_ylabel('频次')
    axes[1, 0].set_title('位移分布直方图')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 第四张图：速度分布直方图
    axes[1, 1].hist(velocities, bins=50, alpha=0.7, color='lightcoral', edgecolor='black')
    axes[1, 1].axvline(x=vel_mean, color='blue', linestyle='--', linewidth=2,
                       label=f'均值: {vel_mean:.6f}μm/s')
    axes[1, 1].set_xlabel('速度 (μm/s)')
    axes[1, 1].set_ylabel('频次')
    axes[1, 1].set_title('速度分布直方图')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    filename = f'{save_prefix}_visualization.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"📊 可视化图表已保存到: {filename}")
    
    return filename


async def main():
    """主函数"""
    print("="*60)
    print("DSA实时数据输出程序")
    print("功能: 直接输出5秒的位移和速度数据并绘图")
    print("数据源: DSA数据收集器")
    print("="*60)
    
    # 检查DLL文件
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    if not os.path.exists(dll_path):
        print(f"错误：找不到DLL文件 {dll_path}")
        return
    
    # 1. 创建DSA数据源
    print("步骤1: 创建DSA数据源...")
    bridge = RealTimeVisualizationBridge(dll_path)
    
    # 启动位移数据收集
    if not await bridge.start_displacement_visualization():
        print("启动DSA位移数据收集失败")
        return
    displacement_source = bridge.get_displacement_source()
    
    # 启动速度数据收集
    if not await bridge.start_velocity_visualization():
        print("启动DSA速度数据收集失败")
        bridge.stop_all()
        return
    velocity_source = bridge.get_velocity_source()
    
    # 2. 等待数据收集稳定
    print("步骤2: 等待数据收集稳定...")
    await asyncio.sleep(2.0)
    
    # 3. 收集5秒的实时数据
    print("步骤3: 开始收集5秒实时数据...")

    # 先尝试标准模式
    print("尝试标准采样模式...")
    data_records = await collect_real_time_data(
        displacement_source=displacement_source,
        velocity_source=velocity_source,
        duration_seconds=5.0
    )

    # 如果采样率太低，尝试激进模式
    actual_rate = len(data_records) / 5.0 if len(data_records) > 0 else 0
    if actual_rate < 100:  # 如果实际采样率低于100Hz
        print(f"⚠️ 标准模式采样率过低 ({actual_rate:.1f} Hz)，尝试激进模式...")
        await asyncio.sleep(1.0)  # 短暂等待

        data_records = await collect_real_time_data_aggressive(
            displacement_source=displacement_source,
            velocity_source=velocity_source,
            duration_seconds=5.0
        )

        # 如果激进模式仍然不够，尝试超激进模式
        actual_rate = len(data_records) / 5.0 if len(data_records) > 0 else 0
        if actual_rate < 150:  # 如果激进模式采样率仍低于150Hz
            print(f"⚠️ 激进模式采样率仍然过低 ({actual_rate:.1f} Hz)，尝试超激进模式...")
            await asyncio.sleep(1.0)  # 短暂等待

            data_records = await collect_real_time_data_ultra_aggressive(
                displacement_source=displacement_source,
                velocity_source=velocity_source,
                duration_seconds=5.0
            )
    
    # 4. 保存数据到文件
    print("步骤4: 保存数据到文件...")
    data_filename = save_data_to_file(data_records, "output_data.txt")
    
    # 5. 生成可视化图表
    print("步骤5: 生成可视化图表...")
    viz_filename = create_visualization(data_records, "output")
    
    # 6. 停止数据收集
    print("步骤6: 停止数据收集...")
    bridge.stop_all()
    
    # 7. 输出统计结果
    print("\n" + "="*60)
    print("📊 数据收集统计结果")
    print("="*60)
    
    if data_records:
        displacements = np.array([r['displacement'] for r in data_records])
        velocities = np.array([r['velocity'] for r in data_records])
        
        print(f"⏱️  数据收集时长: 5.0 秒")
        print(f"📊 总数据点数: {len(data_records)}")
        print(f"🎯 平均采样率: {len(data_records)/5.0:.1f} Hz (目标: 200Hz)")
        print(f"📍 位移统计: 均值={np.mean(displacements):.6f}μm, 标准差={np.std(displacements):.6f}μm")
        print(f"📍 位移范围: {np.min(displacements):.6f} ~ {np.max(displacements):.6f} μm")
        print(f"🚀 速度统计: 均值={np.mean(velocities):.6f}μm/s, 标准差={np.std(velocities):.6f}μm/s")
        print(f"🚀 速度范围: {np.min(velocities):.6f} ~ {np.max(velocities):.6f} μm/s")
        
        print(f"\n💾 生成的文件:")
        print(f"  📄 {data_filename} - 位移和速度数据")
        print(f"  📊 {viz_filename} - 可视化图表")
    
    print("\n✅ 程序执行完成！")
    print("="*60)


if __name__ == "__main__":
    asyncio.run(main())
