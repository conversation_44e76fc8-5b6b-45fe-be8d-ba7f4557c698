#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DSA数据收集器诊断脚本
用于诊断为什么数据收集在短时间内停止
"""

import asyncio
import time
import os
from real_time_data_bridge import RealTimeVisualizationBridge

async def diagnose_dsa_collector():
    """诊断DSA数据收集器状态"""
    print("="*60)
    print("🔍 DSA数据收集器诊断程序")
    print("="*60)
    
    # 检查DLL文件
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    if not os.path.exists(dll_path):
        print(f"❌ 错误：找不到DLL文件 {dll_path}")
        return
    
    bridge = None
    
    try:
        # 1. 初始化数据桥接器
        print("步骤1: 初始化数据桥接器...")
        bridge = RealTimeVisualizationBridge(dll_path)
        
        # 2. 启动数据收集
        print("步骤2: 启动DSA数据收集...")
        if not await bridge.start_displacement_visualization():
            print("❌ 启动DSA位移数据收集失败")
            return
        
        if not await bridge.start_velocity_visualization():
            print("❌ 启动DSA速度数据收集失败")
            bridge.stop_all()
            return
        
        # 获取数据源
        displacement_source = bridge.get_displacement_source()
        velocity_source = bridge.get_velocity_source()
        
        print("✅ DSA数据收集器启动成功")
        print("⏳ 等待数据稳定...")
        await asyncio.sleep(2.0)
        
        # 3. 持续监控10秒
        print("步骤3: 开始10秒诊断监控...")
        start_time = time.time()
        data_count = 0
        last_check_time = start_time
        
        while True:
            current_time = time.time()
            elapsed = current_time - start_time
            
            # 检查是否达到10秒
            if elapsed >= 10.0:
                print(f"✅ 诊断完成，总时长: {elapsed:.3f}s")
                break
            
            # 检查数据源状态
            if hasattr(displacement_source, 'is_connected'):
                disp_connected = displacement_source.is_connected
            else:
                disp_connected = "未知"
                
            if hasattr(velocity_source, 'is_connected'):
                vel_connected = velocity_source.is_connected
            else:
                vel_connected = "未知"
            
            # 检查底层收集器状态
            collector_running = "未知"
            if hasattr(displacement_source, 'collector'):
                collector = displacement_source.collector
                if hasattr(collector, 'is_running'):
                    collector_running = collector.is_running
            
            # 尝试读取数据
            try:
                displacement_data = await displacement_source.read_data()
                velocity_data = await velocity_source.read_data()
                
                if displacement_data is not None:
                    data_count += 1
                    
            except Exception as e:
                print(f"❌ 数据读取异常 (时间: {elapsed:.3f}s): {e}")
                break
            
            # 每2秒显示一次状态
            if (current_time - last_check_time) >= 2.0:
                print(f"📊 时间: {elapsed:.1f}s | 位移连接: {disp_connected} | 速度连接: {vel_connected} | 收集器运行: {collector_running} | 数据点: {data_count}")
                last_check_time = current_time
            
            # 短暂等待
            await asyncio.sleep(0.01)  # 10ms
        
        print(f"\n📊 诊断结果:")
        print(f"  总运行时间: {elapsed:.3f}s")
        print(f"  收集数据点: {data_count}")
        print(f"  平均采样率: {data_count/elapsed:.1f} Hz")
        
    except Exception as e:
        print(f"❌ 诊断过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        if bridge is not None:
            print("\n🧹 清理资源...")
            bridge.stop_all()
    
    print("\n✅ 诊断程序完成！")
    print("="*60)

if __name__ == "__main__":
    asyncio.run(diagnose_dsa_collector())
